# Markdown 主题编辑器

一个功能强大的 Markdown 编辑和展示工具，支持自定义主题、字体、字号、主题色等，可以动态添加和管理主题。

## 🚀 功能特性

### 核心功能
- 📝 **实时编辑预览**: 左侧编辑，右侧实时预览
- 🎨 **多主题支持**: 内置默认、深色、GitHub 等多种主题
- ⚙️ **完全可定制**: 字体、字号、颜色、间距等完全可配置
- 💾 **主题管理**: 保存、导入、导出自定义主题
- 🌙 **深色模式**: 护眼的深色主题支持

### 编辑器功能
- 🔤 **语法高亮**: 支持 Markdown 语法高亮
- 📊 **代码高亮**: 支持多种编程语言的代码块高亮
- 📏 **行号显示**: 可选的行号显示
- 🔄 **自动换行**: 可配置的自动换行
- 🗺️ **小地图**: 可选的代码小地图

### 用户体验
- ⌨️ **快捷键支持**: 常用操作的快捷键
- 🛠️ **工具栏**: 常用 Markdown 格式化工具
- 📊 **状态栏**: 显示文档统计信息和编辑器状态
- 📱 **响应式设计**: 支持不同屏幕尺寸

## 🛠️ 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Vite
- **编辑器**: Monaco Editor
- **Markdown 解析**: marked
- **代码高亮**: highlight.js
- **图标**: Lucide React
- **样式**: 自定义 CSS + CSS Variables

## 📦 安装和运行

### 环境要求
- Node.js 18+ (推荐使用 Node.js 20+)
- npm 或 yarn

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

应用将在 `http://localhost:5173` 启动。

### 构建生产版本
```bash
npm run build
```

### 预览生产版本
```bash
npm run preview
```

## 🎨 主题系统

### 内置主题
1. **默认主题**: 简洁的浅色主题
2. **深色主题**: 护眼的深色主题
3. **GitHub 主题**: 仿 GitHub 风格的主题

### 自定义主题
- 支持完全自定义颜色配置
- 可配置字体、字号、行高等排版属性
- 支持导入/导出主题文件
- 主题自动保存到本地存储

## ⌨️ 快捷键

- `Ctrl/Cmd + S`: 保存文档
- `Ctrl/Cmd + P`: 切换预览模式
- `Ctrl/Cmd + T`: 切换主题
- `Ctrl/Cmd + N`: 新建文档
- `Ctrl/Cmd + E`: 导出文档

## 🔧 配置选项

### 编辑器设置
- 字体系列选择
- 字号调节 (10-24px)
- 行高调节 (1.2-2.0)
- 行号显示开关
- 自动换行开关
- 小地图显示开关

### 主题配置
- 主色调
- 次要色
- 背景色
- 表面色
- 文本色
- 边框色
- 强调色

## 📁 项目结构

```
src/
├── components/          # React 组件
│   ├── MarkdownEditor.tsx
│   ├── MarkdownPreview.tsx
│   ├── ThemeSelector.tsx
│   ├── SettingsPanel.tsx
│   ├── StatusBar.tsx
│   └── Toolbar.tsx
├── contexts/           # React Context
│   └── ThemeContext.tsx
├── hooks/              # 自定义 Hooks
│   └── useKeyboardShortcuts.ts
├── themes/             # 主题配置
│   └── presets.ts
├── types/              # TypeScript 类型定义
│   └── theme.ts
├── styles/             # 样式文件
│   └── markdown.css
├── App.tsx             # 主应用组件
├── main.tsx            # 应用入口
└── index.css           # 全局样式
```

## 🎯 使用说明

1. **编辑 Markdown**: 在左侧编辑器中输入 Markdown 内容
2. **实时预览**: 右侧会实时显示渲染后的效果
3. **切换视图**: 使用顶部按钮切换编辑、预览或分屏模式
4. **选择主题**: 点击主题选择器切换不同主题
5. **自定义设置**: 点击设置按钮调整编辑器和主题配置
6. **导入导出**: 在主题选择器中可以导入/导出主题文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
