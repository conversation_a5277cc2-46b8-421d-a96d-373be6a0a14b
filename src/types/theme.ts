interface ThemeConfig {
  id: string;
  name: string;
  description?: string;
  colors: {
    primary: string;
    secondary: string;
    background: string;
    surface: string;
    text: string;
    textSecondary: string;
    border: string;
    accent: string;
  };
  typography: {
    fontFamily: string;
    fontSize: number;
    lineHeight: number;
    headingFontFamily?: string;
    codeFontFamily: string;
  };
  spacing: {
    small: string;
    medium: string;
    large: string;
  };
  borderRadius: string;
  shadows: {
    small: string;
    medium: string;
    large: string;
  };
}

interface EditorSettings {
  theme: string;
  fontSize: number;
  fontFamily: string;
  lineHeight: number;
  showLineNumbers: boolean;
  wordWrap: boolean;
  minimap: boolean;
}

interface AppState {
  currentTheme: ThemeConfig;
  editorSettings: EditorSettings;
  markdownContent: string;
  availableThemes: ThemeConfig[];
}

export type { ThemeConfig, EditorSettings, AppState };
