/* Markdown Preview Styles */
.markdown-preview {
  font-family: var(--preview-font-family);
  font-size: var(--preview-font-size);
  line-height: var(--preview-line-height);
  color: var(--preview-text);
  background-color: var(--preview-bg);
}

.markdown-preview h1,
.markdown-preview h2,
.markdown-preview h3,
.markdown-preview h4,
.markdown-preview h5,
.markdown-preview h6 {
  font-family: var(--preview-heading-font);
  font-weight: 600;
  margin-top: var(--preview-spacing-large);
  margin-bottom: var(--preview-spacing-medium);
  color: var(--preview-text);
}

.markdown-preview h1 {
  font-size: 2.25em;
  border-bottom: 2px solid var(--preview-border);
  padding-bottom: var(--preview-spacing-small);
}

.markdown-preview h2 {
  font-size: 1.875em;
  border-bottom: 1px solid var(--preview-border);
  padding-bottom: var(--preview-spacing-small);
}

.markdown-preview h3 {
  font-size: 1.5em;
}

.markdown-preview h4 {
  font-size: 1.25em;
}

.markdown-preview h5 {
  font-size: 1.125em;
}

.markdown-preview h6 {
  font-size: 1em;
  color: var(--preview-text-secondary);
}

.markdown-preview p {
  margin-bottom: var(--preview-spacing-medium);
  line-height: var(--preview-line-height);
}

.markdown-preview a {
  color: var(--preview-primary);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: border-color 0.2s ease;
}

.markdown-preview a:hover {
  border-bottom-color: var(--preview-primary);
}

.markdown-preview strong {
  font-weight: 600;
  color: var(--preview-text);
}

.markdown-preview em {
  font-style: italic;
}

.markdown-preview code.inline-code {
  font-family: var(--preview-code-font);
  font-size: 0.875em;
  background-color: var(--preview-surface);
  color: var(--preview-accent);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  border: 1px solid var(--preview-border);
}

.markdown-preview pre {
  background-color: var(--preview-surface);
  border: 1px solid var(--preview-border);
  border-radius: var(--preview-border-radius);
  padding: var(--preview-spacing-medium);
  margin: var(--preview-spacing-medium) 0;
  overflow-x: auto;
  box-shadow: var(--preview-shadow-small);
}

.markdown-preview pre code {
  font-family: var(--preview-code-font);
  font-size: 0.875em;
  background: none;
  border: none;
  padding: 0;
  color: var(--preview-text);
}

.markdown-preview blockquote {
  border-left: 4px solid var(--preview-primary);
  background-color: var(--preview-surface);
  margin: var(--preview-spacing-medium) 0;
  padding: var(--preview-spacing-small) var(--preview-spacing-medium);
  border-radius: 0 var(--preview-border-radius) var(--preview-border-radius) 0;
}

.markdown-preview blockquote p {
  margin: 0;
  color: var(--preview-text-secondary);
  font-style: italic;
}

.markdown-preview ul,
.markdown-preview ol {
  margin: var(--preview-spacing-medium) 0;
  padding-left: var(--preview-spacing-large);
}

.markdown-preview li {
  margin-bottom: var(--preview-spacing-small);
  line-height: var(--preview-line-height);
}

.markdown-preview li::marker {
  color: var(--preview-primary);
}

.markdown-preview ul ul,
.markdown-preview ol ol,
.markdown-preview ul ol,
.markdown-preview ol ul {
  margin: var(--preview-spacing-small) 0;
}

.markdown-preview .table-wrapper {
  overflow-x: auto;
  margin: var(--preview-spacing-medium) 0;
  border-radius: var(--preview-border-radius);
  box-shadow: var(--preview-shadow-small);
}

.markdown-preview .markdown-table {
  width: 100%;
  border-collapse: collapse;
  background-color: var(--preview-bg);
  border: 1px solid var(--preview-border);
}

.markdown-preview .markdown-table th,
.markdown-preview .markdown-table td {
  padding: var(--preview-spacing-small) var(--preview-spacing-medium);
  text-align: left;
  border-bottom: 1px solid var(--preview-border);
}

.markdown-preview .markdown-table th {
  background-color: var(--preview-surface);
  font-weight: 600;
  color: var(--preview-text);
  border-bottom: 2px solid var(--preview-border);
}

.markdown-preview .markdown-table tr:hover {
  background-color: var(--preview-surface);
}

.markdown-preview .markdown-table tr:last-child td {
  border-bottom: none;
}

.markdown-preview hr {
  border: none;
  height: 1px;
  background-color: var(--preview-border);
  margin: var(--preview-spacing-large) 0;
}

/* Highlight.js theme overrides */
.markdown-preview .hljs {
  background: var(--preview-surface) !important;
  color: var(--preview-text) !important;
}

.markdown-preview .hljs-keyword,
.markdown-preview .hljs-selector-tag,
.markdown-preview .hljs-built_in {
  color: var(--preview-primary) !important;
}

.markdown-preview .hljs-string,
.markdown-preview .hljs-attr {
  color: var(--preview-accent) !important;
}

.markdown-preview .hljs-comment,
.markdown-preview .hljs-quote {
  color: var(--preview-text-secondary) !important;
  font-style: italic;
}

.markdown-preview .hljs-number,
.markdown-preview .hljs-literal {
  color: var(--preview-secondary) !important;
}

.markdown-preview .hljs-function,
.markdown-preview .hljs-title {
  color: var(--preview-primary) !important;
  font-weight: 600;
}

.markdown-preview .hljs-variable,
.markdown-preview .hljs-name {
  color: var(--preview-text) !important;
}

.markdown-preview .hljs-tag {
  color: var(--preview-primary) !important;
}

.markdown-preview .hljs-regexp {
  color: var(--preview-accent) !important;
}

.markdown-preview .hljs-symbol,
.markdown-preview .hljs-bullet,
.markdown-preview .hljs-link {
  color: var(--preview-secondary) !important;
}

.markdown-preview .hljs-emphasis {
  font-style: italic;
}

.markdown-preview .hljs-strong {
  font-weight: bold;
}

/* Responsive design */
@media (max-width: 768px) {
  .markdown-preview {
    padding: var(--preview-spacing-medium);
  }
  
  .markdown-preview h1 {
    font-size: 1.875em;
  }
  
  .markdown-preview h2 {
    font-size: 1.5em;
  }
  
  .markdown-preview .table-wrapper {
    font-size: 0.875em;
  }
}
