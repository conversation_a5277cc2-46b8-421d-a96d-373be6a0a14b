import React from 'react';
import { ThemeProvider } from './contexts/ThemeContext';
import MarkdownEditor from './components/MarkdownEditor';
import MarkdownPreview from './components/MarkdownPreview';
import ThemeSelector from './components/ThemeSelector';
import SettingsPanel from './components/SettingsPanel';
import { useTheme } from './contexts/ThemeContext';
import { FileText, Eye, Split } from 'lucide-react';
import 'highlight.js/styles/github.css';
import './styles/markdown.css';

const AppContent: React.FC = () => {
  const { appState } = useTheme();
  const [viewMode, setViewMode] = React.useState<'split' | 'editor' | 'preview'>('split');

  const appStyles = React.useMemo(() => ({
    backgroundColor: appState.currentTheme.colors.background,
    color: appState.currentTheme.colors.text,
    minHeight: '100vh',
  }), [appState.currentTheme]);

  return (
    <div style={appStyles} className="flex flex-col h-screen">
      {/* Header */}
      <header
        className="flex items-center justify-between p-4 border-b"
        style={{ borderColor: appState.currentTheme.colors.border }}
      >
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-bold" style={{ color: appState.currentTheme.colors.text }}>
            Markdown 主题编辑器
          </h1>
          <div className="flex items-center gap-2">
            <button
              onClick={() => setViewMode('split')}
              className={`p-2 rounded transition-colors ${viewMode === 'split' ? 'font-medium' : ''}`}
              style={{
                backgroundColor: viewMode === 'split'
                  ? appState.currentTheme.colors.primary + '20'
                  : 'transparent',
                color: viewMode === 'split'
                  ? appState.currentTheme.colors.primary
                  : appState.currentTheme.colors.textSecondary,
              }}
              title="分屏模式"
            >
              <Split size={16} />
            </button>
            <button
              onClick={() => setViewMode('editor')}
              className={`p-2 rounded transition-colors ${viewMode === 'editor' ? 'font-medium' : ''}`}
              style={{
                backgroundColor: viewMode === 'editor'
                  ? appState.currentTheme.colors.primary + '20'
                  : 'transparent',
                color: viewMode === 'editor'
                  ? appState.currentTheme.colors.primary
                  : appState.currentTheme.colors.textSecondary,
              }}
              title="编辑模式"
            >
              <FileText size={16} />
            </button>
            <button
              onClick={() => setViewMode('preview')}
              className={`p-2 rounded transition-colors ${viewMode === 'preview' ? 'font-medium' : ''}`}
              style={{
                backgroundColor: viewMode === 'preview'
                  ? appState.currentTheme.colors.primary + '20'
                  : 'transparent',
                color: viewMode === 'preview'
                  ? appState.currentTheme.colors.primary
                  : appState.currentTheme.colors.textSecondary,
              }}
              title="预览模式"
            >
              <Eye size={16} />
            </button>
          </div>
        </div>
        <div className="flex items-center gap-4">
          <ThemeSelector />
          <SettingsPanel />
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex overflow-hidden">
        {viewMode === 'split' && (
          <>
            <div className="flex-1 border-r" style={{ borderColor: appState.currentTheme.colors.border }}>
              <MarkdownEditor />
            </div>
            <div className="flex-1">
              <MarkdownPreview />
            </div>
          </>
        )}
        {viewMode === 'editor' && (
          <div className="flex-1">
            <MarkdownEditor />
          </div>
        )}
        {viewMode === 'preview' && (
          <div className="flex-1">
            <MarkdownPreview />
          </div>
        )}
      </main>
    </div>
  );
};

function App() {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
}

export default App;
