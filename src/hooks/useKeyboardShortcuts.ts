import { useEffect } from 'react';

interface KeyboardShortcuts {
  onSave?: () => void;
  onTogglePreview?: () => void;
  onToggleTheme?: () => void;
  onNewFile?: () => void;
  onExport?: () => void;
}

export const useKeyboardShortcuts = (shortcuts: KeyboardShortcuts) => {
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl/Cmd + S - Save
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        shortcuts.onSave?.();
      }
      
      // Ctrl/Cmd + P - Toggle Preview
      if ((event.ctrlKey || event.metaKey) && event.key === 'p') {
        event.preventDefault();
        shortcuts.onTogglePreview?.();
      }
      
      // Ctrl/Cmd + T - Toggle Theme
      if ((event.ctrlKey || event.metaKey) && event.key === 't') {
        event.preventDefault();
        shortcuts.onToggleTheme?.();
      }
      
      // Ctrl/Cmd + N - New File
      if ((event.ctrlKey || event.metaKey) && event.key === 'n') {
        event.preventDefault();
        shortcuts.onNewFile?.();
      }
      
      // Ctrl/Cmd + E - Export
      if ((event.ctrlKey || event.metaKey) && event.key === 'e') {
        event.preventDefault();
        shortcuts.onExport?.();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [shortcuts]);
};
