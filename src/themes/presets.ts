import { ThemeConfig } from '../types/theme';

export const defaultTheme: ThemeConfig = {
  id: 'default',
  name: '默认主题',
  description: '简洁的默认主题',
  colors: {
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    surface: '#f8fafc',
    text: '#1e293b',
    textSecondary: '#64748b',
    border: '#e2e8f0',
    accent: '#06b6d4',
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: 16,
    lineHeight: 1.6,
    headingFontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    codeFontFamily: '"Fira Code", "JetBrains Mono", Consolas, monospace',
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '2rem',
  },
  borderRadius: '0.5rem',
  shadows: {
    small: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    medium: '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    large: '0 10px 15px -3px rgb(0 0 0 / 0.1)',
  },
};

export const darkTheme: ThemeConfig = {
  id: 'dark',
  name: '深色主题',
  description: '护眼的深色主题',
  colors: {
    primary: '#60a5fa',
    secondary: '#94a3b8',
    background: '#0f172a',
    surface: '#1e293b',
    text: '#f1f5f9',
    textSecondary: '#94a3b8',
    border: '#334155',
    accent: '#22d3ee',
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    fontSize: 16,
    lineHeight: 1.6,
    headingFontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
    codeFontFamily: '"Fira Code", "JetBrains Mono", Consolas, monospace',
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '2rem',
  },
  borderRadius: '0.5rem',
  shadows: {
    small: '0 1px 2px 0 rgb(0 0 0 / 0.3)',
    medium: '0 4px 6px -1px rgb(0 0 0 / 0.4)',
    large: '0 10px 15px -3px rgb(0 0 0 / 0.4)',
  },
};

export const githubTheme: ThemeConfig = {
  id: 'github',
  name: 'GitHub 主题',
  description: '仿 GitHub 风格的主题',
  colors: {
    primary: '#0969da',
    secondary: '#656d76',
    background: '#ffffff',
    surface: '#f6f8fa',
    text: '#24292f',
    textSecondary: '#656d76',
    border: '#d0d7de',
    accent: '#1f883d',
  },
  typography: {
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif',
    fontSize: 16,
    lineHeight: 1.5,
    headingFontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "Noto Sans", Helvetica, Arial, sans-serif',
    codeFontFamily: '"SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace',
  },
  spacing: {
    small: '0.5rem',
    medium: '1rem',
    large: '2rem',
  },
  borderRadius: '0.375rem',
  shadows: {
    small: '0 1px 0 rgba(27,31,36,0.04)',
    medium: '0 8px 24px rgba(140,149,159,0.2)',
    large: '0 12px 28px rgba(140,149,159,0.3)',
  },
};

export const presetThemes: ThemeConfig[] = [
  defaultTheme,
  darkTheme,
  githubTheme,
];
