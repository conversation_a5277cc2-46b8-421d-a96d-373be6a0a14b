import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { ThemeConfig, EditorSettings, AppState } from '../types/theme';
import { presetThemes, defaultTheme } from '../themes/presets';
import { defaultMarkdownContent } from '../data/defaultContent';

interface ThemeContextType {
  appState: AppState;
  updateTheme: (themeId: string) => void;
  updateEditorSettings: (settings: Partial<EditorSettings>) => void;
  updateMarkdownContent: (content: string) => void;
  addCustomTheme: (theme: ThemeConfig) => void;
  removeCustomTheme: (themeId: string) => void;
  exportTheme: (themeId: string) => string;
  importTheme: (themeJson: string) => boolean;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

const defaultEditorSettings: EditorSettings = {
  theme: 'default',
  fontSize: 14,
  fontFamily: '"Fira Code", "JetBrains Mono", Consolas, monospace',
  lineHeight: 1.5,
  showLineNumbers: true,
  wordWrap: true,
  minimap: false,
};



export const ThemeProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [appState, setAppState] = useState<AppState>(() => {
    // 从 localStorage 加载保存的状态
    const savedState = localStorage.getItem('markdown-editor-state');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        return {
          ...parsed,
          availableThemes: [...presetThemes, ...(parsed.customThemes || [])],
          markdownContent: parsed.markdownContent || defaultMarkdownContent,
        };
      } catch (error) {
        console.error('Failed to parse saved state:', error);
      }
    }
    
    return {
      currentTheme: defaultTheme,
      editorSettings: defaultEditorSettings,
      markdownContent: defaultMarkdownContent,
      availableThemes: presetThemes,
    };
  });

  // 保存状态到 localStorage
  useEffect(() => {
    const stateToSave = {
      currentTheme: appState.currentTheme,
      editorSettings: appState.editorSettings,
      markdownContent: appState.markdownContent,
      customThemes: appState.availableThemes.filter(theme => 
        !presetThemes.some(preset => preset.id === theme.id)
      ),
    };
    localStorage.setItem('markdown-editor-state', JSON.stringify(stateToSave));
  }, [appState]);

  const updateTheme = (themeId: string) => {
    const theme = appState.availableThemes.find(t => t.id === themeId);
    if (theme) {
      setAppState(prev => ({
        ...prev,
        currentTheme: theme,
        editorSettings: {
          ...prev.editorSettings,
          theme: themeId,
        },
      }));
    }
  };

  const updateEditorSettings = (settings: Partial<EditorSettings>) => {
    setAppState(prev => ({
      ...prev,
      editorSettings: {
        ...prev.editorSettings,
        ...settings,
      },
    }));
  };

  const updateMarkdownContent = (content: string) => {
    setAppState(prev => ({
      ...prev,
      markdownContent: content,
    }));
  };

  const addCustomTheme = (theme: ThemeConfig) => {
    setAppState(prev => ({
      ...prev,
      availableThemes: [...prev.availableThemes, theme],
    }));
  };

  const removeCustomTheme = (themeId: string) => {
    // 不能删除预设主题
    if (presetThemes.some(theme => theme.id === themeId)) {
      return;
    }
    
    setAppState(prev => ({
      ...prev,
      availableThemes: prev.availableThemes.filter(theme => theme.id !== themeId),
      currentTheme: prev.currentTheme.id === themeId ? defaultTheme : prev.currentTheme,
    }));
  };

  const exportTheme = (themeId: string): string => {
    const theme = appState.availableThemes.find(t => t.id === themeId);
    if (!theme) {
      throw new Error('Theme not found');
    }
    return JSON.stringify(theme, null, 2);
  };

  const importTheme = (themeJson: string): boolean => {
    try {
      const theme: ThemeConfig = JSON.parse(themeJson);
      
      // 验证主题结构
      if (!theme.id || !theme.name || !theme.colors || !theme.typography) {
        throw new Error('Invalid theme structure');
      }
      
      // 检查是否已存在同名主题
      const existingTheme = appState.availableThemes.find(t => t.id === theme.id);
      if (existingTheme) {
        theme.id = `${theme.id}-${Date.now()}`;
        theme.name = `${theme.name} (导入)`;
      }
      
      addCustomTheme(theme);
      return true;
    } catch (error) {
      console.error('Failed to import theme:', error);
      return false;
    }
  };

  const contextValue: ThemeContextType = {
    appState,
    updateTheme,
    updateEditorSettings,
    updateMarkdownContent,
    addCustomTheme,
    removeCustomTheme,
    exportTheme,
    importTheme,
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
