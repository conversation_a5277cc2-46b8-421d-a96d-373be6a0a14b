export const defaultMarkdownContent = `# Markdown 主题编辑器

欢迎使用 Markdown 主题编辑器！这是一个功能强大的工具，让你可以：

## 主要功能

- 📝 **实时编辑**: 左侧编辑，右侧实时预览
- 🎨 **主题切换**: 多种预设主题可选
- ⚙️ **自定义配置**: 字体、字号、颜色等完全可定制
- 💾 **主题管理**: 保存、导入、导出自定义主题
- 🌙 **深色模式**: 护眼的深色主题支持

## 代码高亮

支持多种编程语言的语法高亮：

\`\`\`javascript
function hello(name) {
  console.log("Hello, " + name + "!");
}

hello("World");
\`\`\`

\`\`\`python
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

print(fibonacci(10))
\`\`\`

## 表格支持

| 功能 | 状态 | 描述 |
|------|------|------|
| 编辑器 | ✅ | Monaco Editor |
| 预览 | ✅ | 实时渲染 |
| 主题 | ✅ | 多主题支持 |
| 导出 | 🚧 | 开发中 |

## 引用和列表

> 这是一个引用块，可以用来突出重要信息。

- 无序列表项 1
- 无序列表项 2
  - 嵌套列表项
  - 另一个嵌套项

1. 有序列表项 1
2. 有序列表项 2
3. 有序列表项 3

开始编辑吧！🚀
`;
