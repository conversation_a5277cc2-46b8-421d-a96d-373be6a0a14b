import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { 
  Bold, 
  Italic, 
  Link, 
  Code, 
  List, 
  ListOrdered, 
  Quote, 
  Image,
  Table,
  Heading1,
  Heading2,
  Heading3,
  Minus
} from 'lucide-react';

interface ToolbarProps {
  onInsertText: (text: string, cursorOffset?: number) => void;
  className?: string;
}

const Toolbar: React.FC<ToolbarProps> = ({ onInsertText, className = '' }) => {
  const { appState } = useTheme();

  const toolbarItems = [
    {
      icon: <Heading1 size={16} />,
      title: '标题 1 (Ctrl+1)',
      action: () => onInsertText('# ', 0),
    },
    {
      icon: <Heading2 size={16} />,
      title: '标题 2 (Ctrl+2)',
      action: () => onInsertText('## ', 0),
    },
    {
      icon: <Heading3 size={16} />,
      title: '标题 3 (Ctrl+3)',
      action: () => onInsertText('### ', 0),
    },
    { type: 'separator' },
    {
      icon: <Bold size={16} />,
      title: '粗体 (Ctrl+B)',
      action: () => onInsertText('**粗体文本**', -4),
    },
    {
      icon: <Italic size={16} />,
      title: '斜体 (Ctrl+I)',
      action: () => onInsertText('*斜体文本*', -4),
    },
    { type: 'separator' },
    {
      icon: <Link size={16} />,
      title: '链接 (Ctrl+K)',
      action: () => onInsertText('[链接文本](https://example.com)', -21),
    },
    {
      icon: <Image size={16} />,
      title: '图片',
      action: () => onInsertText('![图片描述](图片链接)', -5),
    },
    { type: 'separator' },
    {
      icon: <Code size={16} />,
      title: '行内代码',
      action: () => onInsertText('`代码`', -3),
    },
    {
      icon: <Quote size={16} />,
      title: '引用',
      action: () => onInsertText('> 引用文本', -4),
    },
    { type: 'separator' },
    {
      icon: <List size={16} />,
      title: '无序列表',
      action: () => onInsertText('- 列表项', -3),
    },
    {
      icon: <ListOrdered size={16} />,
      title: '有序列表',
      action: () => onInsertText('1. 列表项', -3),
    },
    { type: 'separator' },
    {
      icon: <Table size={16} />,
      title: '表格',
      action: () => onInsertText(
        '| 列1 | 列2 | 列3 |\n|-----|-----|-----|\n| 内容1 | 内容2 | 内容3 |',
        -35
      ),
    },
    {
      icon: <Minus size={16} />,
      title: '分割线',
      action: () => onInsertText('---\n', 0),
    },
  ];

  return (
    <div
      className={`flex items-center gap-1 px-4 py-2 border-b ${className}`}
      style={{
        backgroundColor: appState.currentTheme.colors.surface,
        borderColor: appState.currentTheme.colors.border,
      }}
    >
      {toolbarItems.map((item, index) => {
        if (item.type === 'separator') {
          return (
            <div
              key={index}
              className="w-px h-6 mx-2"
              style={{ backgroundColor: appState.currentTheme.colors.border }}
            />
          );
        }

        return (
          <button
            key={index}
            onClick={item.action}
            className="p-2 rounded hover:bg-opacity-10 hover:bg-gray-500 transition-colors"
            title={item.title}
            style={{ color: appState.currentTheme.colors.textSecondary }}
          >
            {item.icon}
          </button>
        );
      })}
    </div>
  );
};

export default Toolbar;
