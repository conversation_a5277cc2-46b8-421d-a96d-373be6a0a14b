import React from 'react';
import { useTheme } from '../contexts/ThemeContext';
import { FileText, Eye, Clock, Hash } from 'lucide-react';

interface StatusBarProps {
  viewMode: 'split' | 'editor' | 'preview';
  className?: string;
}

const StatusBar: React.FC<StatusBarProps> = ({ viewMode, className = '' }) => {
  const { appState } = useTheme();
  
  // 计算文档统计信息
  const stats = React.useMemo(() => {
    const content = appState.markdownContent;
    const lines = content.split('\n').length;
    const words = content.trim() ? content.trim().split(/\s+/).length : 0;
    const characters = content.length;
    const charactersNoSpaces = content.replace(/\s/g, '').length;
    
    return {
      lines,
      words,
      characters,
      charactersNoSpaces,
    };
  }, [appState.markdownContent]);

  const getViewModeIcon = () => {
    switch (viewMode) {
      case 'editor':
        return <FileText size={14} />;
      case 'preview':
        return <Eye size={14} />;
      case 'split':
      default:
        return <Hash size={14} />;
    }
  };

  const getViewModeText = () => {
    switch (viewMode) {
      case 'editor':
        return '编辑模式';
      case 'preview':
        return '预览模式';
      case 'split':
      default:
        return '分屏模式';
    }
  };

  return (
    <div
      className={`flex items-center justify-between px-4 py-2 text-sm border-t ${className}`}
      style={{
        backgroundColor: appState.currentTheme.colors.surface,
        borderColor: appState.currentTheme.colors.border,
        color: appState.currentTheme.colors.textSecondary,
      }}
    >
      <div className="flex items-center gap-4">
        {/* 视图模式 */}
        <div className="flex items-center gap-2">
          {getViewModeIcon()}
          <span>{getViewModeText()}</span>
        </div>
        
        {/* 当前主题 */}
        <div className="flex items-center gap-2">
          <div
            className="w-3 h-3 rounded-full border"
            style={{
              backgroundColor: appState.currentTheme.colors.primary,
              borderColor: appState.currentTheme.colors.border,
            }}
          />
          <span>{appState.currentTheme.name}</span>
        </div>
      </div>

      <div className="flex items-center gap-6">
        {/* 文档统计 */}
        <div className="flex items-center gap-4">
          <span title="行数">行: {stats.lines}</span>
          <span title="单词数">词: {stats.words}</span>
          <span title="字符数">字符: {stats.characters}</span>
          <span title="字符数(不含空格)">字符(无空格): {stats.charactersNoSpaces}</span>
        </div>
        
        {/* 编辑器设置 */}
        <div className="flex items-center gap-4">
          <span title="字体大小">{appState.editorSettings.fontSize}px</span>
          <span title="行高">{appState.editorSettings.lineHeight}</span>
        </div>
        
        {/* 时间 */}
        <div className="flex items-center gap-2">
          <Clock size={14} />
          <span>{new Date().toLocaleTimeString()}</span>
        </div>
      </div>
    </div>
  );
};

export default StatusBar;
