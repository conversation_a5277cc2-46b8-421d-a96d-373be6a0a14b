import React, { useRef, useEffect } from 'react';
import Editor from '@monaco-editor/react';
import { useTheme } from '../contexts/ThemeContext';

interface MarkdownEditorProps {
  className?: string;
}

const MarkdownEditor: React.FC<MarkdownEditorProps> = ({ className = '' }) => {
  const { appState, updateMarkdownContent } = useTheme();
  const editorRef = useRef<any>(null);

  const handleEditorDidMount = (editor: any, monaco: any) => {
    editorRef.current = editor;
    
    // 配置 Markdown 语言支持
    monaco.languages.setMonarchTokensProvider('markdown', {
      tokenizer: {
        root: [
          [/^#{1,6}\s.*$/, 'markup.heading'],
          [/^\s*\*\s/, 'markup.list'],
          [/^\s*-\s/, 'markup.list'],
          [/^\s*\+\s/, 'markup.list'],
          [/^\s*\d+\.\s/, 'markup.list'],
          [/\*\*.*?\*\*/, 'markup.bold'],
          [/__.*?__/, 'markup.bold'],
          [/\*.*?\*/, 'markup.italic'],
          [/_.*?_/, 'markup.italic'],
          [/`.*?`/, 'markup.inline.raw'],
          [/```[\s\S]*?```/, 'markup.fenced_code'],
          [/\[.*?\]\(.*?\)/, 'markup.underline.link'],
          [/^>.*$/, 'markup.quote'],
          [/^\|.*\|$/, 'markup.table'],
        ],
      },
    });

    // 设置编辑器主题
    monaco.editor.defineTheme('custom-theme', {
      base: appState.currentTheme.id === 'dark' ? 'vs-dark' : 'vs',
      inherit: true,
      rules: [
        { token: 'markup.heading', foreground: appState.currentTheme.colors.primary.replace('#', '') },
        { token: 'markup.bold', fontStyle: 'bold' },
        { token: 'markup.italic', fontStyle: 'italic' },
        { token: 'markup.inline.raw', foreground: appState.currentTheme.colors.accent.replace('#', '') },
        { token: 'markup.fenced_code', foreground: appState.currentTheme.colors.accent.replace('#', '') },
        { token: 'markup.underline.link', foreground: appState.currentTheme.colors.primary.replace('#', '') },
        { token: 'markup.quote', foreground: appState.currentTheme.colors.textSecondary.replace('#', '') },
        { token: 'markup.list', foreground: appState.currentTheme.colors.primary.replace('#', '') },
        { token: 'markup.table', foreground: appState.currentTheme.colors.secondary.replace('#', '') },
      ],
      colors: {
        'editor.background': appState.currentTheme.colors.background,
        'editor.foreground': appState.currentTheme.colors.text,
        'editor.lineHighlightBackground': appState.currentTheme.colors.surface,
        'editor.selectionBackground': appState.currentTheme.colors.primary + '30',
        'editorLineNumber.foreground': appState.currentTheme.colors.textSecondary,
        'editorLineNumber.activeForeground': appState.currentTheme.colors.primary,
        'editor.selectionHighlightBackground': appState.currentTheme.colors.primary + '20',
        'editorCursor.foreground': appState.currentTheme.colors.primary,
      },
    });

    monaco.editor.setTheme('custom-theme');
  };

  // 当主题改变时更新编辑器主题
  useEffect(() => {
    if (editorRef.current) {
      const monaco = (window as any).monaco;
      if (monaco) {
        monaco.editor.defineTheme('custom-theme', {
          base: appState.currentTheme.id === 'dark' ? 'vs-dark' : 'vs',
          inherit: true,
          rules: [
            { token: 'markup.heading', foreground: appState.currentTheme.colors.primary.replace('#', '') },
            { token: 'markup.bold', fontStyle: 'bold' },
            { token: 'markup.italic', fontStyle: 'italic' },
            { token: 'markup.inline.raw', foreground: appState.currentTheme.colors.accent.replace('#', '') },
            { token: 'markup.fenced_code', foreground: appState.currentTheme.colors.accent.replace('#', '') },
            { token: 'markup.underline.link', foreground: appState.currentTheme.colors.primary.replace('#', '') },
            { token: 'markup.quote', foreground: appState.currentTheme.colors.textSecondary.replace('#', '') },
            { token: 'markup.list', foreground: appState.currentTheme.colors.primary.replace('#', '') },
            { token: 'markup.table', foreground: appState.currentTheme.colors.secondary.replace('#', '') },
          ],
          colors: {
            'editor.background': appState.currentTheme.colors.background,
            'editor.foreground': appState.currentTheme.colors.text,
            'editor.lineHighlightBackground': appState.currentTheme.colors.surface,
            'editor.selectionBackground': appState.currentTheme.colors.primary + '30',
            'editorLineNumber.foreground': appState.currentTheme.colors.textSecondary,
            'editorLineNumber.activeForeground': appState.currentTheme.colors.primary,
            'editor.selectionHighlightBackground': appState.currentTheme.colors.primary + '20',
            'editorCursor.foreground': appState.currentTheme.colors.primary,
          },
        });
        monaco.editor.setTheme('custom-theme');
      }
    }
  }, [appState.currentTheme]);

  const handleEditorChange = (value: string | undefined) => {
    if (value !== undefined) {
      updateMarkdownContent(value);
    }
  };

  return (
    <div className={`h-full ${className}`}>
      <Editor
        height="100%"
        language="markdown"
        value={appState.markdownContent}
        onChange={handleEditorChange}
        onMount={handleEditorDidMount}
        options={{
          fontSize: appState.editorSettings.fontSize,
          fontFamily: appState.editorSettings.fontFamily,
          lineHeight: appState.editorSettings.lineHeight,
          lineNumbers: appState.editorSettings.showLineNumbers ? 'on' : 'off',
          wordWrap: appState.editorSettings.wordWrap ? 'on' : 'off',
          minimap: {
            enabled: appState.editorSettings.minimap,
          },
          scrollBeyondLastLine: false,
          automaticLayout: true,
          tabSize: 2,
          insertSpaces: true,
          renderWhitespace: 'selection',
          bracketPairColorization: {
            enabled: true,
          },
          smoothScrolling: true,
          cursorBlinking: 'smooth',
          cursorSmoothCaretAnimation: 'on',
        }}
      />
    </div>
  );
};

export default MarkdownEditor;
