import React, { useState } from 'react';
import { Palette, Download, Upload, Trash2, Plus } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { ThemeConfig } from '../types/theme';

interface ThemeSelectorProps {
  className?: string;
}

const ThemeSelector: React.FC<ThemeSelectorProps> = ({ className = '' }) => {
  const { 
    appState, 
    updateTheme, 
    exportTheme, 
    importTheme, 
    removeCustomTheme,
    addCustomTheme 
  } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [newThemeName, setNewThemeName] = useState('');

  const handleThemeSelect = (themeId: string) => {
    updateTheme(themeId);
    setIsOpen(false);
  };

  const handleExportTheme = (themeId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    try {
      const themeJson = exportTheme(themeId);
      const blob = new Blob([themeJson], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${themeId}-theme.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      alert('导出主题失败');
    }
  };

  const handleImportTheme = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const content = e.target?.result as string;
      if (importTheme(content)) {
        alert('主题导入成功！');
      } else {
        alert('主题导入失败，请检查文件格式');
      }
    };
    reader.readAsText(file);
    event.target.value = '';
  };

  const handleDeleteTheme = (themeId: string, event: React.MouseEvent) => {
    event.stopPropagation();
    if (confirm('确定要删除这个主题吗？')) {
      removeCustomTheme(themeId);
    }
  };

  const handleCreateTheme = () => {
    if (!newThemeName.trim()) return;

    const newTheme: ThemeConfig = {
      ...appState.currentTheme,
      id: `custom-${Date.now()}`,
      name: newThemeName.trim(),
      description: '自定义主题',
    };

    addCustomTheme(newTheme);
    updateTheme(newTheme.id);
    setNewThemeName('');
    setShowCreateForm(false);
    setIsOpen(false);
  };

  const isCustomTheme = (themeId: string) => {
    return !['default', 'dark', 'github'].includes(themeId);
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors"
        style={{
          backgroundColor: appState.currentTheme.colors.surface,
          borderColor: appState.currentTheme.colors.border,
          color: appState.currentTheme.colors.text,
        }}
      >
        <Palette size={16} />
        <span>{appState.currentTheme.name}</span>
      </button>

      {isOpen && (
        <div
          className="absolute top-full left-0 mt-2 w-80 rounded-lg border shadow-lg z-50 max-h-96 overflow-y-auto"
          style={{
            backgroundColor: appState.currentTheme.colors.surface,
            borderColor: appState.currentTheme.colors.border,
            boxShadow: appState.currentTheme.shadows.medium,
          }}
        >
          <div className="p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="font-semibold" style={{ color: appState.currentTheme.colors.text }}>
                选择主题
              </h3>
              <div className="flex gap-2">
                <label className="cursor-pointer p-1 rounded hover:bg-opacity-10 hover:bg-gray-500">
                  <Upload size={16} style={{ color: appState.currentTheme.colors.textSecondary }} />
                  <input
                    type="file"
                    accept=".json"
                    onChange={handleImportTheme}
                    className="hidden"
                  />
                </label>
                <button
                  onClick={() => setShowCreateForm(!showCreateForm)}
                  className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500"
                >
                  <Plus size={16} style={{ color: appState.currentTheme.colors.textSecondary }} />
                </button>
              </div>
            </div>

            {showCreateForm && (
              <div className="mb-4 p-3 rounded border" style={{ borderColor: appState.currentTheme.colors.border }}>
                <input
                  type="text"
                  placeholder="新主题名称"
                  value={newThemeName}
                  onChange={(e) => setNewThemeName(e.target.value)}
                  className="w-full px-3 py-2 rounded border mb-2"
                  style={{
                    backgroundColor: appState.currentTheme.colors.background,
                    borderColor: appState.currentTheme.colors.border,
                    color: appState.currentTheme.colors.text,
                  }}
                  onKeyPress={(e) => e.key === 'Enter' && handleCreateTheme()}
                />
                <div className="flex gap-2">
                  <button
                    onClick={handleCreateTheme}
                    className="px-3 py-1 rounded text-sm"
                    style={{
                      backgroundColor: appState.currentTheme.colors.primary,
                      color: 'white',
                    }}
                  >
                    创建
                  </button>
                  <button
                    onClick={() => {
                      setShowCreateForm(false);
                      setNewThemeName('');
                    }}
                    className="px-3 py-1 rounded text-sm"
                    style={{
                      backgroundColor: appState.currentTheme.colors.surface,
                      borderColor: appState.currentTheme.colors.border,
                      color: appState.currentTheme.colors.textSecondary,
                    }}
                  >
                    取消
                  </button>
                </div>
              </div>
            )}

            <div className="space-y-2">
              {appState.availableThemes.map((theme) => (
                <div
                  key={theme.id}
                  className={`flex items-center justify-between p-3 rounded cursor-pointer transition-colors ${
                    theme.id === appState.currentTheme.id ? 'ring-2' : ''
                  }`}
                  style={{
                    backgroundColor: theme.id === appState.currentTheme.id 
                      ? appState.currentTheme.colors.primary + '20' 
                      : 'transparent',
                    ringColor: theme.id === appState.currentTheme.id 
                      ? appState.currentTheme.colors.primary 
                      : 'transparent',
                  }}
                  onClick={() => handleThemeSelect(theme.id)}
                >
                  <div className="flex items-center gap-3">
                    <div
                      className="w-6 h-6 rounded border"
                      style={{
                        backgroundColor: theme.colors.primary,
                        borderColor: theme.colors.border,
                      }}
                    />
                    <div>
                      <div className="font-medium" style={{ color: appState.currentTheme.colors.text }}>
                        {theme.name}
                      </div>
                      {theme.description && (
                        <div className="text-sm" style={{ color: appState.currentTheme.colors.textSecondary }}>
                          {theme.description}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-1">
                    <button
                      onClick={(e) => handleExportTheme(theme.id, e)}
                      className="p-1 rounded hover:bg-opacity-10 hover:bg-gray-500"
                      title="导出主题"
                    >
                      <Download size={14} style={{ color: appState.currentTheme.colors.textSecondary }} />
                    </button>
                    {isCustomTheme(theme.id) && (
                      <button
                        onClick={(e) => handleDeleteTheme(theme.id, e)}
                        className="p-1 rounded hover:bg-opacity-10 hover:bg-red-500"
                        title="删除主题"
                      >
                        <Trash2 size={14} style={{ color: appState.currentTheme.colors.textSecondary }} />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ThemeSelector;
