import React, { useState } from 'react';
import { Settings, X, Type, Palette } from 'lucide-react';
import { useTheme } from '../contexts/ThemeContext';
import { ThemeConfig } from '../types/theme';

interface SettingsPanelProps {
  className?: string;
}

const SettingsPanel: React.FC<SettingsPanelProps> = ({ className = '' }) => {
  const { appState, updateEditorSettings, updateTheme, addCustomTheme } = useTheme();
  const [isOpen, setIsOpen] = useState(false);
  const [activeTab, setActiveTab] = useState<'editor' | 'theme'>('editor');
  const [customTheme, setCustomTheme] = useState<ThemeConfig>(appState.currentTheme);

  const fontFamilies = [
    { name: 'Fira Code', value: '"Fira Code", "JetBrains Mono", Consolas, monospace' },
    { name: 'JetBrains Mono', value: '"JetBrains Mono", "Fira Code", Consolas, monospace' },
    { name: 'Consolas', value: 'Consolas, "Courier New", monospace' },
    { name: 'Monaco', value: 'Monaco, "Lucida Console", monospace' },
    { name: 'Source Code Pro', value: '"Source Code Pro", monospace' },
    { name: 'Menlo', value: 'Menlo, Monaco, "Courier New", monospace' },
  ];

  const handleColorChange = (colorKey: keyof ThemeConfig['colors'], value: string) => {
    setCustomTheme(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorKey]: value,
      },
    }));
  };

  const handleTypographyChange = (key: keyof ThemeConfig['typography'], value: string | number) => {
    setCustomTheme(prev => ({
      ...prev,
      typography: {
        ...prev.typography,
        [key]: value,
      },
    }));
  };

  const handleApplyCustomTheme = () => {
    const newTheme: ThemeConfig = {
      ...customTheme,
      id: `custom-${Date.now()}`,
      name: `${customTheme.name} (自定义)`,
      description: '自定义主题配置',
    };
    
    addCustomTheme(newTheme);
    updateTheme(newTheme.id);
  };

  const resetCustomTheme = () => {
    setCustomTheme(appState.currentTheme);
  };

  return (
    <>
      <button
        onClick={() => setIsOpen(true)}
        className="flex items-center gap-2 px-4 py-2 rounded-lg border transition-colors"
        style={{
          backgroundColor: appState.currentTheme.colors.surface,
          borderColor: appState.currentTheme.colors.border,
          color: appState.currentTheme.colors.text,
        }}
      >
        <Settings size={16} />
        <span>设置</span>
      </button>

      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div
            className="w-full max-w-4xl h-5/6 rounded-lg shadow-xl flex flex-col"
            style={{
              backgroundColor: appState.currentTheme.colors.background,
              borderColor: appState.currentTheme.colors.border,
            }}
          >
            {/* Header */}
            <div
              className="flex items-center justify-between p-4 border-b"
              style={{ borderColor: appState.currentTheme.colors.border }}
            >
              <h2 className="text-xl font-semibold" style={{ color: appState.currentTheme.colors.text }}>
                设置
              </h2>
              <button
                onClick={() => setIsOpen(false)}
                className="p-2 rounded hover:bg-opacity-10 hover:bg-gray-500"
              >
                <X size={20} style={{ color: appState.currentTheme.colors.textSecondary }} />
              </button>
            </div>

            <div className="flex flex-1 overflow-hidden">
              {/* Sidebar */}
              <div
                className="w-64 border-r p-4"
                style={{ borderColor: appState.currentTheme.colors.border }}
              >
                <nav className="space-y-2">
                  <button
                    onClick={() => setActiveTab('editor')}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded transition-colors ${
                      activeTab === 'editor' ? 'font-medium' : ''
                    }`}
                    style={{
                      backgroundColor: activeTab === 'editor' 
                        ? appState.currentTheme.colors.primary + '20' 
                        : 'transparent',
                      color: activeTab === 'editor' 
                        ? appState.currentTheme.colors.primary 
                        : appState.currentTheme.colors.text,
                    }}
                  >
                    <Type size={16} />
                    编辑器设置
                  </button>
                  <button
                    onClick={() => setActiveTab('theme')}
                    className={`w-full flex items-center gap-3 px-3 py-2 rounded transition-colors ${
                      activeTab === 'theme' ? 'font-medium' : ''
                    }`}
                    style={{
                      backgroundColor: activeTab === 'theme' 
                        ? appState.currentTheme.colors.primary + '20' 
                        : 'transparent',
                      color: activeTab === 'theme' 
                        ? appState.currentTheme.colors.primary 
                        : appState.currentTheme.colors.text,
                    }}
                  >
                    <Palette size={16} />
                    主题定制
                  </button>
                </nav>
              </div>

              {/* Content */}
              <div className="flex-1 p-6 overflow-y-auto">
                {activeTab === 'editor' && (
                  <div className="space-y-6">
                    <h3 className="text-lg font-semibold" style={{ color: appState.currentTheme.colors.text }}>
                      编辑器配置
                    </h3>

                    {/* Font Family */}
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: appState.currentTheme.colors.text }}>
                        字体
                      </label>
                      <select
                        value={appState.editorSettings.fontFamily}
                        onChange={(e) => updateEditorSettings({ fontFamily: e.target.value })}
                        className="w-full px-3 py-2 rounded border"
                        style={{
                          backgroundColor: appState.currentTheme.colors.surface,
                          borderColor: appState.currentTheme.colors.border,
                          color: appState.currentTheme.colors.text,
                        }}
                      >
                        {fontFamilies.map((font) => (
                          <option key={font.value} value={font.value}>
                            {font.name}
                          </option>
                        ))}
                      </select>
                    </div>

                    {/* Font Size */}
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: appState.currentTheme.colors.text }}>
                        字号: {appState.editorSettings.fontSize}px
                      </label>
                      <input
                        type="range"
                        min="10"
                        max="24"
                        value={appState.editorSettings.fontSize}
                        onChange={(e) => updateEditorSettings({ fontSize: parseInt(e.target.value) })}
                        className="w-full"
                      />
                    </div>

                    {/* Line Height */}
                    <div>
                      <label className="block text-sm font-medium mb-2" style={{ color: appState.currentTheme.colors.text }}>
                        行高: {appState.editorSettings.lineHeight}
                      </label>
                      <input
                        type="range"
                        min="1.2"
                        max="2.0"
                        step="0.1"
                        value={appState.editorSettings.lineHeight}
                        onChange={(e) => updateEditorSettings({ lineHeight: parseFloat(e.target.value) })}
                        className="w-full"
                      />
                    </div>

                    {/* Checkboxes */}
                    <div className="space-y-3">
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={appState.editorSettings.showLineNumbers}
                          onChange={(e) => updateEditorSettings({ showLineNumbers: e.target.checked })}
                        />
                        <span style={{ color: appState.currentTheme.colors.text }}>显示行号</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={appState.editorSettings.wordWrap}
                          onChange={(e) => updateEditorSettings({ wordWrap: e.target.checked })}
                        />
                        <span style={{ color: appState.currentTheme.colors.text }}>自动换行</span>
                      </label>
                      <label className="flex items-center gap-2">
                        <input
                          type="checkbox"
                          checked={appState.editorSettings.minimap}
                          onChange={(e) => updateEditorSettings({ minimap: e.target.checked })}
                        />
                        <span style={{ color: appState.currentTheme.colors.text }}>显示小地图</span>
                      </label>
                    </div>
                  </div>
                )}

                {activeTab === 'theme' && (
                  <div className="space-y-6">
                    <div className="flex items-center justify-between">
                      <h3 className="text-lg font-semibold" style={{ color: appState.currentTheme.colors.text }}>
                        主题定制
                      </h3>
                      <div className="flex gap-2">
                        <button
                          onClick={resetCustomTheme}
                          className="px-3 py-1 rounded text-sm border"
                          style={{
                            borderColor: appState.currentTheme.colors.border,
                            color: appState.currentTheme.colors.textSecondary,
                          }}
                        >
                          重置
                        </button>
                        <button
                          onClick={handleApplyCustomTheme}
                          className="px-3 py-1 rounded text-sm"
                          style={{
                            backgroundColor: appState.currentTheme.colors.primary,
                            color: 'white',
                          }}
                        >
                          应用主题
                        </button>
                      </div>
                    </div>

                    {/* Colors */}
                    <div>
                      <h4 className="font-medium mb-4" style={{ color: appState.currentTheme.colors.text }}>
                        颜色配置
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        {Object.entries(customTheme.colors).map(([key, value]) => (
                          <div key={key}>
                            <label className="block text-sm font-medium mb-1" style={{ color: appState.currentTheme.colors.text }}>
                              {key === 'primary' && '主色'}
                              {key === 'secondary' && '次要色'}
                              {key === 'background' && '背景色'}
                              {key === 'surface' && '表面色'}
                              {key === 'text' && '文本色'}
                              {key === 'textSecondary' && '次要文本色'}
                              {key === 'border' && '边框色'}
                              {key === 'accent' && '强调色'}
                            </label>
                            <div className="flex gap-2">
                              <input
                                type="color"
                                value={value}
                                onChange={(e) => handleColorChange(key as keyof ThemeConfig['colors'], e.target.value)}
                                className="w-12 h-8 rounded border"
                                style={{ borderColor: appState.currentTheme.colors.border }}
                              />
                              <input
                                type="text"
                                value={value}
                                onChange={(e) => handleColorChange(key as keyof ThemeConfig['colors'], e.target.value)}
                                className="flex-1 px-2 py-1 rounded border text-sm"
                                style={{
                                  backgroundColor: appState.currentTheme.colors.surface,
                                  borderColor: appState.currentTheme.colors.border,
                                  color: appState.currentTheme.colors.text,
                                }}
                              />
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Typography */}
                    <div>
                      <h4 className="font-medium mb-4" style={{ color: appState.currentTheme.colors.text }}>
                        字体配置
                      </h4>
                      <div className="space-y-4">
                        <div>
                          <label className="block text-sm font-medium mb-1" style={{ color: appState.currentTheme.colors.text }}>
                            字体族
                          </label>
                          <input
                            type="text"
                            value={customTheme.typography.fontFamily}
                            onChange={(e) => handleTypographyChange('fontFamily', e.target.value)}
                            className="w-full px-3 py-2 rounded border"
                            style={{
                              backgroundColor: appState.currentTheme.colors.surface,
                              borderColor: appState.currentTheme.colors.border,
                              color: appState.currentTheme.colors.text,
                            }}
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1" style={{ color: appState.currentTheme.colors.text }}>
                            字号: {customTheme.typography.fontSize}px
                          </label>
                          <input
                            type="range"
                            min="12"
                            max="20"
                            value={customTheme.typography.fontSize}
                            onChange={(e) => handleTypographyChange('fontSize', parseInt(e.target.value))}
                            className="w-full"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-medium mb-1" style={{ color: appState.currentTheme.colors.text }}>
                            行高: {customTheme.typography.lineHeight}
                          </label>
                          <input
                            type="range"
                            min="1.2"
                            max="2.0"
                            step="0.1"
                            value={customTheme.typography.lineHeight}
                            onChange={(e) => handleTypographyChange('lineHeight', parseFloat(e.target.value))}
                            className="w-full"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SettingsPanel;
