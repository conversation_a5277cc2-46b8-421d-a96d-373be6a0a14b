import React, { useMemo } from 'react';
import { marked } from 'marked';
import hljs from 'highlight.js';
import { useTheme } from '../contexts/ThemeContext';

interface MarkdownPreviewProps {
  className?: string;
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ className = '' }) => {
  const { appState } = useTheme();

  const htmlContent = useMemo(() => {
    try {
      // 确保 markdownContent 是字符串类型
      const content = String(appState.markdownContent || '');

      if (!content.trim()) {
        return '<div><p>开始编辑 Markdown 内容...</p></div>';
      }

      // 配置 marked 选项
      marked.setOptions({
        breaks: true,
        gfm: true,
        sanitize: false,
        highlight: function(code, lang) {
          if (lang && hljs.getLanguage(lang)) {
            try {
              return hljs.highlight(code, { language: lang }).value;
            } catch (err) {
              console.warn('Highlight error:', err);
            }
          }
          try {
            return hljs.highlightAuto(code).value;
          } catch (err) {
            return code;
          }
        }
      });

      // 解析 Markdown
      const html = marked.parse(content);
      return html;
    } catch (error) {
      console.error('Markdown parsing error:', error);
      return `
        <div style="padding: 20px; border: 1px solid #ff6b6b; border-radius: 8px; background-color: #ffe0e0;">
          <h3 style="color: #d63031; margin-top: 0;">Markdown 解析错误</h3>
          <p>请检查 Markdown 语法是否正确。</p>
          <details>
            <summary>错误详情</summary>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">${String(error)}</pre>
          </details>
        </div>
      `;
    }
  }, [appState.markdownContent]);

  const previewStyles = useMemo(() => {
    const theme = appState.currentTheme;
    return {
      '--preview-bg': theme.colors.background,
      '--preview-text': theme.colors.text,
      '--preview-text-secondary': theme.colors.textSecondary,
      '--preview-primary': theme.colors.primary,
      '--preview-secondary': theme.colors.secondary,
      '--preview-surface': theme.colors.surface,
      '--preview-border': theme.colors.border,
      '--preview-accent': theme.colors.accent,
      '--preview-font-family': theme.typography.fontFamily,
      '--preview-font-size': `${theme.typography.fontSize}px`,
      '--preview-line-height': theme.typography.lineHeight,
      '--preview-heading-font': theme.typography.headingFontFamily || theme.typography.fontFamily,
      '--preview-code-font': theme.typography.codeFontFamily,
      '--preview-border-radius': theme.borderRadius,
      '--preview-shadow-small': theme.shadows.small,
      '--preview-shadow-medium': theme.shadows.medium,
      '--preview-spacing-small': theme.spacing.small,
      '--preview-spacing-medium': theme.spacing.medium,
      '--preview-spacing-large': theme.spacing.large,
    } as React.CSSProperties;
  }, [appState.currentTheme]);

  return (
    <div 
      className={`markdown-preview h-full overflow-auto p-6 ${className}`}
      style={previewStyles}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
};

export default MarkdownPreview;
