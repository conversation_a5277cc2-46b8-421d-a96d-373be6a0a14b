import React, { useMemo } from 'react';
import { marked } from 'marked';
import hljs from 'highlight.js';
import { useTheme } from '../contexts/ThemeContext';

interface MarkdownPreviewProps {
  className?: string;
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ className = '' }) => {
  const { appState } = useTheme();

  // 配置 marked
  const renderer = useMemo(() => {
    const customRenderer = new marked.Renderer();
    
    // 自定义代码块渲染
    customRenderer.code = (code, language) => {
      const validLanguage = hljs.getLanguage(language || '') ? language : 'plaintext';
      const highlighted = hljs.highlight(code, { language: validLanguage || 'plaintext' }).value;
      return `<pre><code class="hljs language-${validLanguage}">${highlighted}</code></pre>`;
    };

    // 自定义内联代码渲染
    customRenderer.codespan = (code) => {
      return `<code class="inline-code">${code}</code>`;
    };

    // 自定义链接渲染
    customRenderer.link = (href, title, text) => {
      const titleAttr = title ? ` title="${title}"` : '';
      return `<a href="${href}"${titleAttr} target="_blank" rel="noopener noreferrer">${text}</a>`;
    };

    // 自定义表格渲染
    customRenderer.table = (header, body) => {
      return `<div class="table-wrapper"><table class="markdown-table"><thead>${header}</thead><tbody>${body}</tbody></table></div>`;
    };

    return customRenderer;
  }, []);

  // 配置 marked 选项
  marked.setOptions({
    renderer,
    highlight: (code, lang) => {
      const language = hljs.getLanguage(lang) ? lang : 'plaintext';
      return hljs.highlight(code, { language }).value;
    },
    langPrefix: 'hljs language-',
    breaks: true,
    gfm: true,
  });

  const htmlContent = useMemo(() => {
    try {
      return marked(appState.markdownContent);
    } catch (error) {
      console.error('Markdown parsing error:', error);
      return '<p>Markdown 解析错误</p>';
    }
  }, [appState.markdownContent]);

  const previewStyles = useMemo(() => {
    const theme = appState.currentTheme;
    return {
      '--preview-bg': theme.colors.background,
      '--preview-text': theme.colors.text,
      '--preview-text-secondary': theme.colors.textSecondary,
      '--preview-primary': theme.colors.primary,
      '--preview-secondary': theme.colors.secondary,
      '--preview-surface': theme.colors.surface,
      '--preview-border': theme.colors.border,
      '--preview-accent': theme.colors.accent,
      '--preview-font-family': theme.typography.fontFamily,
      '--preview-font-size': `${theme.typography.fontSize}px`,
      '--preview-line-height': theme.typography.lineHeight,
      '--preview-heading-font': theme.typography.headingFontFamily || theme.typography.fontFamily,
      '--preview-code-font': theme.typography.codeFontFamily,
      '--preview-border-radius': theme.borderRadius,
      '--preview-shadow-small': theme.shadows.small,
      '--preview-shadow-medium': theme.shadows.medium,
      '--preview-spacing-small': theme.spacing.small,
      '--preview-spacing-medium': theme.spacing.medium,
      '--preview-spacing-large': theme.spacing.large,
    } as React.CSSProperties;
  }, [appState.currentTheme]);

  return (
    <div 
      className={`markdown-preview h-full overflow-auto p-6 ${className}`}
      style={previewStyles}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
};

export default MarkdownPreview;
