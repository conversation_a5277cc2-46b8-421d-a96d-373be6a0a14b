import React, { useMemo } from 'react';
import { marked } from 'marked';
import hljs from 'highlight.js';
import { useTheme } from '../contexts/ThemeContext';

interface MarkdownPreviewProps {
  className?: string;
}

const MarkdownPreview: React.FC<MarkdownPreviewProps> = ({ className = '' }) => {
  const { appState } = useTheme();

  const htmlContent = useMemo(() => {
    try {
      if (!appState.markdownContent) {
        return '<div><p>开始编辑 Markdown 内容...</p></div>';
      }

      // 简单的 marked 配置
      const html = marked(appState.markdownContent, {
        breaks: true,
        gfm: true,
      });

      return html;
    } catch (error) {
      console.error('Markdown parsing error:', error);
      return '<div><h3>Markdown 解析错误</h3><p>请检查 Markdown 语法是否正确。</p><pre>' + String(error) + '</pre></div>';
    }
  }, [appState.markdownContent]);

  const previewStyles = useMemo(() => {
    const theme = appState.currentTheme;
    return {
      '--preview-bg': theme.colors.background,
      '--preview-text': theme.colors.text,
      '--preview-text-secondary': theme.colors.textSecondary,
      '--preview-primary': theme.colors.primary,
      '--preview-secondary': theme.colors.secondary,
      '--preview-surface': theme.colors.surface,
      '--preview-border': theme.colors.border,
      '--preview-accent': theme.colors.accent,
      '--preview-font-family': theme.typography.fontFamily,
      '--preview-font-size': `${theme.typography.fontSize}px`,
      '--preview-line-height': theme.typography.lineHeight,
      '--preview-heading-font': theme.typography.headingFontFamily || theme.typography.fontFamily,
      '--preview-code-font': theme.typography.codeFontFamily,
      '--preview-border-radius': theme.borderRadius,
      '--preview-shadow-small': theme.shadows.small,
      '--preview-shadow-medium': theme.shadows.medium,
      '--preview-spacing-small': theme.spacing.small,
      '--preview-spacing-medium': theme.spacing.medium,
      '--preview-spacing-large': theme.spacing.large,
    } as React.CSSProperties;
  }, [appState.currentTheme]);

  return (
    <div 
      className={`markdown-preview h-full overflow-auto p-6 ${className}`}
      style={previewStyles}
      dangerouslySetInnerHTML={{ __html: htmlContent }}
    />
  );
};

export default MarkdownPreview;
